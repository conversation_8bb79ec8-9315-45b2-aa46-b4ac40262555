/**
 * Composio MCP Integration Test Component
 * 
 * This component provides a simple interface to test the Composio MCP backend
 * integration and verify that all API endpoints are working correctly.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle, RefreshCw, Zap } from 'lucide-react';
import { ComposioMCPService } from '@/lib/composio-api';
import { ComposioApp, ComposioConnection, ComposioHealthResponse } from '@/types/composio';
import { toast } from 'sonner';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message?: string;
  data?: any;
}

export const ComposioTest: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [supportedApps, setSupportedApps] = useState<ComposioApp[]>([]);
  const [userConnections, setUserConnections] = useState<ComposioConnection[]>([]);
  const [healthStatus, setHealthStatus] = useState<ComposioHealthResponse | null>(null);

  const tests: TestResult[] = [
    { name: 'Health Check', status: 'pending' },
    { name: 'Get Supported Apps', status: 'pending' },
    { name: 'List User Connections', status: 'pending' },
    { name: 'Create Test Connection (Gmail)', status: 'pending' },
    { name: 'Verify Connection Created', status: 'pending' },
    { name: 'Delete Test Connection', status: 'pending' },
  ];

  const [currentTests, setCurrentTests] = useState<TestResult[]>(tests);

  const updateTestResult = (index: number, status: TestResult['status'], message?: string, data?: any) => {
    setCurrentTests(prev => prev.map((test, i) => 
      i === index ? { ...test, status, message, data } : test
    ));
  };

  const runTests = async () => {
    setIsRunning(true);
    setCurrentTests(tests.map(test => ({ ...test, status: 'pending' })));

    try {
      // Test 1: Health Check
      console.log('🔍 Running health check...');
      try {
        const health = await ComposioMCPService.healthCheck();
        setHealthStatus(health);
        updateTestResult(0, 'success', `Service: ${health.service} v${health.version}`, health);
        toast.success('Health check passed!');
      } catch (error) {
        updateTestResult(0, 'error', `Health check failed: ${error}`);
        toast.error('Health check failed');
      }

      // Test 2: Get Supported Apps
      console.log('📱 Fetching supported apps...');
      try {
        const apps = await ComposioMCPService.getSupportedApps();
        setSupportedApps(apps);
        updateTestResult(1, 'success', `Found ${apps.length} supported apps`, apps);
        toast.success(`Loaded ${apps.length} supported apps`);
      } catch (error) {
        updateTestResult(1, 'error', `Failed to fetch apps: ${error}`);
        toast.error('Failed to fetch supported apps');
      }

      // Test 3: List User Connections
      console.log('🔗 Fetching user connections...');
      try {
        const connections = await ComposioMCPService.listUserConnections();
        setUserConnections(connections);
        updateTestResult(2, 'success', `Found ${connections.length} existing connections`, connections);
        toast.success(`Found ${connections.length} existing connections`);
      } catch (error) {
        updateTestResult(2, 'error', `Failed to fetch connections: ${error}`);
        toast.error('Failed to fetch connections');
      }

      // Test 4: Create Test Connection (Gmail)
      console.log('➕ Creating test connection for Gmail...');
      let testConnection: ComposioConnection | null = null;
      try {
        testConnection = await ComposioMCPService.createConnection('gmail');
        updateTestResult(3, 'success', `Created connection: ${testConnection.qualified_name}`, testConnection);
        toast.success('Test connection created successfully!');
      } catch (error) {
        updateTestResult(3, 'error', `Failed to create connection: ${error}`);
        toast.error('Failed to create test connection');
      }

      // Test 5: Verify Connection Created
      console.log('✅ Verifying connection was created...');
      try {
        const isConnected = await ComposioMCPService.isAppConnected('gmail');
        if (isConnected) {
          updateTestResult(4, 'success', 'Connection verified in user connections');
          toast.success('Connection verified!');
        } else {
          updateTestResult(4, 'error', 'Connection not found in user connections');
          toast.error('Connection verification failed');
        }
      } catch (error) {
        updateTestResult(4, 'error', `Verification failed: ${error}`);
        toast.error('Connection verification failed');
      }

      // Test 6: Delete Test Connection
      console.log('🗑️ Cleaning up test connection...');
      try {
        const deleted = await ComposioMCPService.deleteConnection('gmail');
        if (deleted) {
          updateTestResult(5, 'success', 'Test connection deleted successfully');
          toast.success('Test connection cleaned up');
        } else {
          updateTestResult(5, 'error', 'Failed to delete test connection');
          toast.error('Failed to clean up test connection');
        }
      } catch (error) {
        updateTestResult(5, 'error', `Cleanup failed: ${error}`);
        toast.error('Failed to clean up test connection');
      }

      // Refresh connections after cleanup
      try {
        const updatedConnections = await ComposioMCPService.listUserConnections();
        setUserConnections(updatedConnections);
      } catch (error) {
        console.warn('Failed to refresh connections after cleanup:', error);
      }

    } catch (error) {
      console.error('Test suite failed:', error);
      toast.error('Test suite failed');
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return 'secondary';
      case 'success':
        return 'default';
      case 'error':
        return 'destructive';
    }
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Composio MCP Integration Test</h2>
          <p className="text-muted-foreground">
            Test the Composio MCP backend integration and verify all endpoints are working
          </p>
        </div>
        <Button 
          onClick={runTests} 
          disabled={isRunning}
          className="flex items-center gap-2"
        >
          {isRunning ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Zap className="h-4 w-4" />
          )}
          {isRunning ? 'Running Tests...' : 'Run Tests'}
        </Button>
      </div>

      {/* Health Status */}
      {healthStatus && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            Composio MCP Service is healthy: {healthStatus.service} v{healthStatus.version}
          </AlertDescription>
        </Alert>
      )}

      {/* Test Results */}
      <Card>
        <CardHeader>
          <CardTitle>Test Results</CardTitle>
          <CardDescription>
            Backend API endpoint testing results
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {currentTests.map((test, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  {getStatusIcon(test.status)}
                  <span className="font-medium">{test.name}</span>
                </div>
                <div className="flex items-center gap-2">
                  {test.message && (
                    <span className="text-sm text-muted-foreground">{test.message}</span>
                  )}
                  <Badge variant={getStatusColor(test.status)}>
                    {test.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Supported Apps */}
      {supportedApps.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Supported Apps ({supportedApps.length})</CardTitle>
            <CardDescription>
              Apps available for Composio MCP integration
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {supportedApps.map((app) => (
                <div key={app.key} className="flex items-center gap-2 p-2 border rounded">
                  <span className="text-lg">{app.icon}</span>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-sm truncate">{app.name}</p>
                    <p className="text-xs text-muted-foreground">{app.category}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* User Connections */}
      <Card>
        <CardHeader>
          <CardTitle>User Connections ({userConnections.length})</CardTitle>
          <CardDescription>
            Current Composio MCP connections for this user
          </CardDescription>
        </CardHeader>
        <CardContent>
          {userConnections.length === 0 ? (
            <p className="text-muted-foreground text-center py-4">
              No connections found. Run the test to create a temporary connection.
            </p>
          ) : (
            <div className="space-y-3">
              {userConnections.map((connection) => (
                <div key={connection.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">{connection.qualified_name}</p>
                    <p className="text-sm text-muted-foreground">
                      Created: {new Date(connection.created_at).toLocaleString()}
                    </p>
                  </div>
                  <Badge variant={connection.status === 'connected' ? 'default' : 'secondary'}>
                    {connection.status}
                  </Badge>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
