/**
 * Composio MCP Integration Test Page
 * 
 * This page provides a dedicated testing interface for the Composio MCP backend
 * integration. Access via /test-composio in your dashboard.
 */

import { ComposioTest } from '@/components/composio/composio-test';

export default function TestComposioPage() {
  return (
    <div className="container mx-auto py-6">
      <ComposioTest />
    </div>
  );
}

export const metadata = {
  title: 'Composio MCP Test | Atlas Agents',
  description: 'Test the Composio MCP backend integration',
};
