#!/usr/bin/env python3
"""
Test script for Composio MCP integration with Supabase persistence

This test validates:
1. Per-user MCP URL generation and storage
2. URL reuse (no regeneration on subsequent calls)
3. Multiple apps per user
4. User isolation
5. Database persistence and cleanup

Usage:
    python test_composio_persistence.py

Requirements:
    - Valid Supabase credentials in environment
    - Internet connection for Composio API calls
"""

import asyncio
import os
import sys
import uuid
from datetime import datetime
from typing import List, Dict, Any

# Add backend to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.composio_integration import composio_mcp_service
from utils.logger import logger


class ComposioPersistenceTest:
    """Test Composio MCP integration with Supabase persistence"""

    def __init__(self):
        self.test_users = []
        self.test_apps = ["gmail", "slack", "github"]
        self.test_results = []

    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")

        self.test_results.append(
            {"test": test_name, "success": success, "details": details}
        )

    def generate_test_users(self, count: int = 3) -> List[str]:
        """Generate test user IDs as valid UUIDs"""
        users = [str(uuid.uuid4()) for _ in range(count)]
        self.test_users.extend(users)
        return users

    async def test_single_user_single_app(self):
        """Test creating and reusing MCP connection for single user/app"""
        print("\n🧪 Test: Single User, Single App")
        print("=" * 50)

        user_id = self.generate_test_users(1)[0]
        app_key = "gmail"

        try:
            # First call - should create new connection
            print(f"📡 First call for user {user_id}, app {app_key}")
            connection1 = await composio_mcp_service.get_or_create_user_mcp_connection(
                user_id, app_key
            )

            if not connection1.success:
                self.log_test(
                    "Single User/App - First Call",
                    False,
                    f"Failed: {connection1.error}",
                )
                return

            print(f"   MCP URL: {connection1.mcp_url}")
            print(f"   Session UUID: {connection1.session_uuid}")

            # Second call - should reuse existing connection
            print(f"🔄 Second call for same user/app")
            connection2 = await composio_mcp_service.get_or_create_user_mcp_connection(
                user_id, app_key
            )

            if not connection2.success:
                self.log_test(
                    "Single User/App - Second Call",
                    False,
                    f"Failed: {connection2.error}",
                )
                return

            print(f"   MCP URL: {connection2.mcp_url}")
            print(f"   Session UUID: {connection2.session_uuid}")

            # Verify URLs are identical (reused)
            urls_match = connection1.mcp_url == connection2.mcp_url
            sessions_match = connection1.session_uuid == connection2.session_uuid

            if urls_match and sessions_match:
                self.log_test(
                    "Single User/App - URL Reuse",
                    True,
                    "URLs and sessions match - no regeneration",
                )
            else:
                self.log_test(
                    "Single User/App - URL Reuse",
                    False,
                    "URLs or sessions don't match - unexpected regeneration",
                )

        except Exception as e:
            self.log_test("Single User/App Test", False, f"Exception: {e}")

    async def test_single_user_multiple_apps(self):
        """Test creating connections for multiple apps for same user"""
        print("\n🧪 Test: Single User, Multiple Apps")
        print("=" * 50)

        user_id = self.generate_test_users(1)[0]
        connections = {}

        try:
            # Create connections for multiple apps
            for app_key in self.test_apps:
                print(f"📡 Creating connection for {app_key}")
                connection = (
                    await composio_mcp_service.get_or_create_user_mcp_connection(
                        user_id, app_key
                    )
                )

                if connection.success:
                    connections[app_key] = connection
                    print(f"   ✅ {app_key}: {connection.mcp_url}")
                else:
                    print(f"   ❌ {app_key}: {connection.error}")

            # Verify all apps have different URLs
            urls = [conn.mcp_url for conn in connections.values()]
            unique_urls = len(set(urls))

            if unique_urls == len(connections):
                self.log_test(
                    "Multiple Apps - Unique URLs",
                    True,
                    f"All {len(connections)} apps have unique URLs",
                )
            else:
                self.log_test(
                    "Multiple Apps - Unique URLs",
                    False,
                    f"Expected {len(connections)} unique URLs, got {unique_urls}",
                )

            # Test listing user connections
            user_connections = await composio_mcp_service.list_user_mcp_connections(
                user_id
            )

            if len(user_connections) >= len(connections):
                self.log_test(
                    "Multiple Apps - List Connections",
                    True,
                    f"Found {len(user_connections)} stored connections",
                )
            else:
                self.log_test(
                    "Multiple Apps - List Connections",
                    False,
                    f"Expected >= {len(connections)}, found {len(user_connections)}",
                )

        except Exception as e:
            self.log_test("Multiple Apps Test", False, f"Exception: {e}")

    async def test_multiple_users_isolation(self):
        """Test that different users get different URLs (isolation)"""
        print("\n🧪 Test: Multiple Users, Isolation")
        print("=" * 50)

        users = self.generate_test_users(2)
        app_key = "gmail"
        user_connections = {}

        try:
            # Create connections for multiple users
            for user_id in users:
                print(f"📡 Creating connection for user {user_id}")
                connection = (
                    await composio_mcp_service.get_or_create_user_mcp_connection(
                        user_id, app_key
                    )
                )

                if connection.success:
                    user_connections[user_id] = connection
                    print(f"   ✅ URL: {connection.mcp_url}")
                else:
                    print(f"   ❌ Failed: {connection.error}")

            # Verify users have different URLs
            if len(user_connections) == 2:
                user1_url = list(user_connections.values())[0].mcp_url
                user2_url = list(user_connections.values())[1].mcp_url

                if user1_url != user2_url:
                    self.log_test(
                        "User Isolation", True, "Different users have different URLs"
                    )
                else:
                    self.log_test(
                        "User Isolation",
                        False,
                        "Users have identical URLs - isolation failed!",
                    )
            else:
                self.log_test(
                    "User Isolation",
                    False,
                    f"Expected 2 connections, got {len(user_connections)}",
                )

        except Exception as e:
            self.log_test("User Isolation Test", False, f"Exception: {e}")

    async def test_database_operations(self):
        """Test database list and delete operations"""
        print("\n🧪 Test: Database Operations")
        print("=" * 50)

        user_id = self.generate_test_users(1)[0]
        app_key = "slack"

        try:
            # Create a connection
            print(f"📡 Creating connection for testing")
            connection = await composio_mcp_service.get_or_create_user_mcp_connection(
                user_id, app_key
            )

            if not connection.success:
                self.log_test(
                    "Database Ops - Create",
                    False,
                    f"Failed to create: {connection.error}",
                )
                return

            # List connections
            connections = await composio_mcp_service.list_user_mcp_connections(user_id)
            found_connection = any(conn["app_key"] == app_key for conn in connections)

            if found_connection:
                self.log_test(
                    "Database Ops - List",
                    True,
                    f"Found connection in list of {len(connections)} connections",
                )
            else:
                self.log_test(
                    "Database Ops - List", False, "Connection not found in list"
                )
                return

            # Delete connection
            deleted = await composio_mcp_service.delete_user_mcp_connection(
                user_id, app_key
            )

            if deleted:
                self.log_test(
                    "Database Ops - Delete", True, "Successfully deleted connection"
                )
            else:
                self.log_test(
                    "Database Ops - Delete", False, "Failed to delete connection"
                )
                return

            # Verify deletion
            connections_after = await composio_mcp_service.list_user_mcp_connections(
                user_id
            )
            still_exists = any(conn["app_key"] == app_key for conn in connections_after)

            if not still_exists:
                self.log_test(
                    "Database Ops - Verify Delete",
                    True,
                    "Connection successfully removed",
                )
            else:
                self.log_test(
                    "Database Ops - Verify Delete",
                    False,
                    "Connection still exists after deletion",
                )

        except Exception as e:
            self.log_test("Database Operations Test", False, f"Exception: {e}")

    async def cleanup_test_data(self):
        """Clean up all test data"""
        print("\n🧹 Cleaning up test data...")

        cleanup_count = 0
        for user_id in self.test_users:
            try:
                connections = await composio_mcp_service.list_user_mcp_connections(
                    user_id
                )
                for connection in connections:
                    app_key = connection["app_key"]
                    deleted = await composio_mcp_service.delete_user_mcp_connection(
                        user_id, app_key
                    )
                    if deleted:
                        cleanup_count += 1
            except Exception as e:
                print(f"   ⚠️ Error cleaning up user {user_id}: {e}")

        print(f"   ✅ Cleaned up {cleanup_count} test connections")

    def print_summary(self):
        """Print test summary"""
        print("\n📊 Test Summary")
        print("=" * 60)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests

        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")

        if failed_tests > 0:
            print(f"\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['details']}")

        print(f"\n🎯 Persistence Assessment:")
        if passed_tests >= total_tests * 0.9:  # 90% pass rate
            print("✅ EXCELLENT: Composio MCP persistence is working perfectly!")
            print("   Ready for production with per-user URL storage and reuse.")
        elif passed_tests >= total_tests * 0.7:  # 70% pass rate
            print("⚠️ GOOD: Most persistence features working, some issues to address.")
        else:
            print("❌ NEEDS WORK: Significant persistence issues detected.")


async def main():
    """Run all persistence tests"""
    print("🎯 Composio MCP Persistence Test")
    print("=" * 60)
    print(f"Testing at: {datetime.now()}")

    # Check environment
    if not os.getenv("SUPABASE_URL") or not os.getenv("SUPABASE_SERVICE_ROLE_KEY"):
        print("❌ Missing Supabase environment variables!")
        print("   Set SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY")
        return

    tester = ComposioPersistenceTest()

    try:
        # Run all tests
        await tester.test_single_user_single_app()
        await tester.test_single_user_multiple_apps()
        await tester.test_multiple_users_isolation()
        await tester.test_database_operations()

        # Print summary
        tester.print_summary()

        # Cleanup
        await tester.cleanup_test_data()

        print(f"\n💡 Next Steps:")
        print("1. If tests pass, the persistence layer is ready")
        print("2. MCP URLs are being stored and reused correctly")
        print("3. Ready to implement MCP authentication flow")

    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
        await tester.cleanup_test_data()
    except Exception as e:
        print(f"\n💥 Test suite failed: {e}")
        import traceback

        traceback.print_exc()
        await tester.cleanup_test_data()


if __name__ == "__main__":
    asyncio.run(main())
