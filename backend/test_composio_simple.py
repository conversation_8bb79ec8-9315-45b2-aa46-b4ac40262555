#!/usr/bin/env python3
"""
Simple test for Composio MCP integration

This test focuses on the core flow:
1. Generate unique MCP URL from Composio (using exact browser request format)
2. Connect to MCP server to validate it works
3. Look for authentication tools or auth URLs

No Supabase storage - just testing the core Composio integration.
"""

import asyncio
import httpx
import json
import uuid
from datetime import datetime
from services.composio_integration import composio_mcp_service


async def test_composio_api_direct():
    """Test direct Composio API call using exact browser format"""
    print("🧪 Testing Direct Composio API Call")
    print("=" * 50)

    # Generate a test session UUID (like browser does)
    session_uuid = str(uuid.uuid4())
    app_key = "gmail"

    print(f"Session UUID: {session_uuid}")
    print(f"App Key: {app_key}")

    # Use exact request format from your browser
    url = f"https://mcp.composio.dev/api/apps/{app_key}/install"

    # Payload with required framework parameter
    payload = {"uuid": session_uuid, "framework": "mcp"}

    # Exact headers from browser
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json, text/plain, */*",
        "Origin": "https://mcp.composio.dev",
        "Referer": f"https://mcp.composio.dev/{app_key}/{session_uuid}",
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
        "DNT": "1",
        "Sec-Ch-Ua": '"Chromium";v="137", "Not/A)Brand";v="24"',
        "Sec-Ch-Ua-Mobile": "?0",
        "Sec-Ch-Ua-Platform": '"macOS"',
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
    }

    # Exact cookies from browser
    cookies = {
        "uuid": session_uuid,
        "isActiveUser": session_uuid,
    }

    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print(f"\n📡 Making request to: {url}")
            print(f"Payload: {json.dumps(payload)}")

            response = await client.post(
                url, json=payload, headers=headers, cookies=cookies
            )

            print(f"\n📊 Response:")
            print(f"Status Code: {response.status_code}")
            print(f"Headers: {dict(response.headers)}")

            if response.status_code == 200:
                response_data = response.json()
                print(f"✅ Success! Response: {json.dumps(response_data, indent=2)}")

                # Try to extract MCP URL
                mcp_url = extract_mcp_url_from_response(
                    response_data, app_key, session_uuid
                )
                if mcp_url:
                    print(f"🔗 Extracted MCP URL: {mcp_url}")
                    return mcp_url, session_uuid
                else:
                    print(f"❌ Could not extract MCP URL from response")
                    return None, session_uuid
            else:
                print(f"❌ Request failed: {response.text}")
                return None, session_uuid

    except Exception as e:
        print(f"❌ Exception: {e}")
        return None, session_uuid


def extract_mcp_url_from_response(response_data, app_key, session_uuid):
    """Extract MCP URL from Composio response using known patterns"""
    # Try various patterns
    patterns = [
        response_data.get("sseUrl"),
        response_data.get("mcp_url"),
        response_data.get("url"),
        response_data.get("data", {}).get("sseUrl"),
        response_data.get("data", {}).get("mcp_url"),
        response_data.get("data", {}).get("url"),
        # Construct from known patterns if not found
        f"https://mcp.composio.dev/partner/composio/{app_key}/sse?customerId={session_uuid}",
        f"https://mcp.composio.dev/partner/composio/{app_key}/mcp?customerId={session_uuid}",
    ]

    for url in patterns:
        if url and isinstance(url, str) and url.startswith("https://"):
            return url

    return None


async def test_mcp_connection(mcp_url):
    """Test connecting to the MCP URL using existing MCP architecture"""
    print(f"\n🔌 Testing MCP Connection")
    print("=" * 50)
    print(f"MCP URL: {mcp_url}")

    try:
        # Import MCP client components
        from mcp.client.sse import sse_client
        from mcp.client.streamable_http import streamablehttp_client
        from mcp import ClientSession

        auth_tools = []
        connection_successful = False

        # Try SSE connection first (most Composio URLs use SSE)
        if "sse" in mcp_url or "/sse" in mcp_url:
            try:
                print(f"🔄 Attempting SSE connection...")
                async with asyncio.timeout(15):
                    async with sse_client(mcp_url) as (read, write):
                        async with ClientSession(read, write) as session:
                            await session.initialize()
                            print("✅ SSE MCP session initialized successfully!")
                            connection_successful = True

                            # List available tools
                            try:
                                tools_result = await session.list_tools()
                                tools = tools_result.tools
                                print(f"📋 Found {len(tools)} tools:")

                                for tool in tools:
                                    print(f"  - {tool.name}: {tool.description}")

                                    # Look for authentication-related tools
                                    if any(
                                        keyword in tool.name.lower()
                                        for keyword in [
                                            "auth",
                                            "connect",
                                            "login",
                                            "oauth",
                                        ]
                                    ):
                                        auth_tools.append(
                                            {
                                                "name": tool.name,
                                                "description": tool.description,
                                                "schema": tool.inputSchema,
                                            }
                                        )
                                        print(f"    🔐 This looks like an auth tool!")

                            except Exception as e:
                                print(f"⚠️ Could not list tools: {e}")

            except asyncio.TimeoutError:
                print(f"⏰ SSE connection timed out")
            except Exception as e:
                print(f"❌ SSE connection failed: {e}")

        # Try HTTP connection if SSE failed
        if not connection_successful and ("mcp" in mcp_url or "http" in mcp_url):
            try:
                print(f"🔄 Attempting HTTP connection...")
                async with asyncio.timeout(15):
                    async with streamablehttp_client(mcp_url) as (read, write, _):
                        async with ClientSession(read, write) as session:
                            await session.initialize()
                            print("✅ HTTP MCP session initialized successfully!")
                            connection_successful = True

                            # List available tools
                            try:
                                tools_result = await session.list_tools()
                                tools = tools_result.tools
                                print(f"📋 Found {len(tools)} tools:")

                                for tool in tools:
                                    print(f"  - {tool.name}: {tool.description}")

                                    # Look for authentication-related tools
                                    if any(
                                        keyword in tool.name.lower()
                                        for keyword in [
                                            "auth",
                                            "connect",
                                            "login",
                                            "oauth",
                                        ]
                                    ):
                                        auth_tools.append(
                                            {
                                                "name": tool.name,
                                                "description": tool.description,
                                                "schema": tool.inputSchema,
                                            }
                                        )
                                        print(f"    🔐 This looks like an auth tool!")

                            except Exception as e:
                                print(f"⚠️ Could not list tools: {e}")

            except asyncio.TimeoutError:
                print(f"⏰ HTTP connection timed out")
            except Exception as e:
                print(f"❌ HTTP connection failed: {e}")

        # Summary
        if connection_successful:
            print(f"\n✅ MCP Connection Summary:")
            print(f"   Connection: Successful")
            print(f"   Auth tools found: {len(auth_tools)}")

            if auth_tools:
                print(f"   🔐 Authentication tools:")
                for tool in auth_tools:
                    print(f"     - {tool['name']}: {tool['description']}")

                # For now, return the MCP URL as the auth URL
                # In a real implementation, you might call an auth tool to get the actual auth URL
                return mcp_url
            else:
                print(
                    f"   ℹ️ No explicit auth tools found - MCP URL might be the auth URL"
                )
                return mcp_url
        else:
            print(f"\n❌ Could not connect to MCP server")
            return None

    except Exception as e:
        print(f"❌ MCP connection test failed: {e}")
        return None


async def test_simplified_service():
    """Test the simplified service method"""
    print(f"\n🚀 Testing Simplified Service Method")
    print("=" * 50)

    user_id = f"test_user_{int(datetime.now().timestamp())}"
    app_key = "gmail"

    try:
        # Use the simplified method (no Supabase storage)
        result = await composio_mcp_service.create_user_mcp_connection_simple(
            user_id, app_key
        )

        if result.success:
            print(f"✅ Service test successful!")
            print(f"   App: {result.app_key}")
            print(f"   MCP URL: {result.mcp_url}")
            print(f"   Auth URL: {result.auth_url}")
            print(f"   Session UUID: {result.session_uuid}")
        else:
            print(f"❌ Service test failed: {result.error}")

        return result

    except Exception as e:
        print(f"❌ Service test exception: {e}")
        return None


async def main():
    """Run all tests"""
    print("🎯 Composio MCP Integration - Simple Test")
    print("=" * 60)
    print(f"Testing at: {datetime.now()}")

    try:
        # Test 1: Direct API call
        mcp_url, session_uuid = await test_composio_api_direct()

        # Test 2: MCP connection (if we got a URL)
        auth_url = None
        if mcp_url:
            auth_url = await test_mcp_connection(mcp_url)

        # Test 3: Simplified service method
        service_result = await test_simplified_service()

        # Summary
        print(f"\n📊 Test Summary")
        print("=" * 60)
        print(f"✅ Direct API call: {'Success' if mcp_url else 'Failed'}")
        print(f"✅ MCP connection: {'Success' if auth_url else 'Failed'}")
        print(
            f"✅ Service method: {'Success' if service_result and service_result.success else 'Failed'}"
        )

        if mcp_url and auth_url:
            print(f"\n🎉 Core flow working!")
            print(f"   1. ✅ Generated unique MCP URL from Composio")
            print(f"   2. ✅ Successfully connected to MCP server")
            print(f"   3. ✅ Got auth URL for user authentication")
            print(f"\n🔗 URLs:")
            print(f"   MCP URL: {mcp_url}")
            print(f"   Auth URL: {auth_url}")
        else:
            print(f"\n⚠️ Some parts of the flow need work")

    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Test failed: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
