#!/usr/bin/env python3
"""
Practical test script for Composio MCP integration

This script tests the actual integration flow without mocks to validate
that the system works end-to-end with real APIs.

Usage:
    python test_composio_mcp_flow.py

Requirements:
    - Backend server running on localhost:8000
    - Valid Supabase credentials in environment
    - Internet connection for Composio API calls
"""

import asyncio
import httpx
import json
import os
import sys
from datetime import datetime
from typing import Dict, Any

# Add backend to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.composio_integration import composio_mcp_service
from utils.logger import logger

class ComposioMCPFlowTester:
    """Test the complete Composio MCP integration flow"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.test_user_id = f"test_user_{int(datetime.now().timestamp())}"
        self.test_results = []
    
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "details": details
        })
    
    async def test_service_direct(self):
        """Test the Composio service directly"""
        print("\n🧪 Testing Composio Service Directly")
        print("=" * 50)
        
        try:
            # Test 1: Session UUID generation
            app_key = "gmail"
            uuid1 = composio_mcp_service._generate_session_uuid(self.test_user_id, app_key)
            uuid2 = composio_mcp_service._generate_session_uuid(self.test_user_id, app_key)
            
            if uuid1 != uuid2 and len(uuid1) == 36:
                self.log_test("Session UUID Generation", True, f"Generated unique UUIDs: {uuid1[:8]}...")
            else:
                self.log_test("Session UUID Generation", False, "UUIDs not unique or invalid format")
            
            # Test 2: MCP URL extraction
            test_response = {
                "sseUrl": "https://mcp.composio.dev/test/url"
            }
            extracted_url = composio_mcp_service._extract_mcp_url(test_response, app_key, uuid1)
            
            if extracted_url == "https://mcp.composio.dev/test/url":
                self.log_test("MCP URL Extraction", True, f"Extracted: {extracted_url}")
            else:
                self.log_test("MCP URL Extraction", False, f"Expected test URL, got: {extracted_url}")
            
            # Test 3: Complete flow (this will make real API calls)
            print(f"\n🌐 Testing complete flow for user {self.test_user_id}")
            connection = await composio_mcp_service.create_user_mcp_connection(
                self.test_user_id, app_key
            )
            
            if connection.success:
                self.log_test("Complete Service Flow", True, 
                    f"MCP URL: {connection.mcp_url}, Auth URL: {connection.auth_url}")
            else:
                self.log_test("Complete Service Flow", False, f"Error: {connection.error}")
                
        except Exception as e:
            self.log_test("Service Direct Test", False, f"Exception: {e}")
    
    async def test_api_endpoints(self):
        """Test the FastAPI endpoints"""
        print("\n🌐 Testing API Endpoints")
        print("=" * 50)
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                # Test 1: Health check
                response = await client.get(f"{self.base_url}/api/composio-mcp/health")
                if response.status_code == 200:
                    self.log_test("Health Endpoint", True, f"Status: {response.json()['status']}")
                else:
                    self.log_test("Health Endpoint", False, f"Status code: {response.status_code}")
                
                # Test 2: Supported apps
                response = await client.get(f"{self.base_url}/api/composio-mcp/supported-apps")
                if response.status_code == 200:
                    data = response.json()
                    app_count = len(data.get("apps", []))
                    self.log_test("Supported Apps Endpoint", True, f"Found {app_count} supported apps")
                else:
                    self.log_test("Supported Apps Endpoint", False, f"Status code: {response.status_code}")
                
                # Test 3: Create connection (requires auth - will likely fail without proper JWT)
                try:
                    response = await client.post(
                        f"{self.base_url}/api/composio-mcp/create-connection",
                        json={"app_key": "gmail"},
                        headers={"Authorization": "Bearer test_token"}  # This will likely fail
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        self.log_test("Create Connection Endpoint", data.get("success", False), 
                            f"Response: {data.get('message', 'No message')}")
                    elif response.status_code == 401:
                        self.log_test("Create Connection Endpoint", True, 
                            "Expected 401 (auth required) - endpoint is protected correctly")
                    else:
                        self.log_test("Create Connection Endpoint", False, 
                            f"Unexpected status: {response.status_code}")
                        
                except Exception as e:
                    self.log_test("Create Connection Endpoint", False, f"Exception: {e}")
                
            except Exception as e:
                self.log_test("API Endpoints Test", False, f"Exception: {e}")
    
    async def test_composio_api_direct(self):
        """Test direct calls to Composio API"""
        print("\n🔗 Testing Direct Composio API Calls")
        print("=" * 50)
        
        try:
            session_uuid = composio_mcp_service._generate_session_uuid(self.test_user_id, "gmail")
            
            # Test status check
            url = await composio_mcp_service._generate_composio_mcp_url("gmail", session_uuid)
            
            if url and url.startswith("https://"):
                self.log_test("Composio API Call", True, f"Generated URL: {url}")
                
                # Test if the URL is accessible
                try:
                    async with httpx.AsyncClient(timeout=10.0) as client:
                        response = await client.get(url)
                        self.log_test("MCP URL Accessibility", True, 
                            f"Status: {response.status_code}, Content-Type: {response.headers.get('content-type', 'unknown')}")
                except Exception as e:
                    self.log_test("MCP URL Accessibility", False, f"Connection error: {e}")
            else:
                self.log_test("Composio API Call", False, "No valid URL generated")
                
        except Exception as e:
            self.log_test("Composio API Direct Test", False, f"Exception: {e}")
    
    async def test_database_operations(self):
        """Test database storage operations"""
        print("\n🗄️ Testing Database Operations")
        print("=" * 50)
        
        try:
            # Test storing a connection
            test_mcp_url = "https://mcp.composio.dev/test/url"
            test_session_uuid = "test-session-uuid"
            
            stored = await composio_mcp_service._store_mcp_connection(
                self.test_user_id, "gmail", test_mcp_url, test_session_uuid
            )
            
            if stored:
                self.log_test("Database Storage", True, "Successfully stored MCP connection")
                
                # Test retrieving connections
                try:
                    from supabase import create_client
                    supabase = create_client(
                        os.getenv("SUPABASE_URL"),
                        os.getenv("SUPABASE_SERVICE_ROLE_KEY")
                    )
                    
                    result = supabase.table("mcp_oauth_tokens").select("*").eq(
                        "user_id", self.test_user_id
                    ).like("qualified_name", "composio/%").execute()
                    
                    if result.data:
                        self.log_test("Database Retrieval", True, 
                            f"Found {len(result.data)} stored connections")
                        
                        # Clean up test data
                        supabase.table("mcp_oauth_tokens").delete().eq(
                            "user_id", self.test_user_id
                        ).execute()
                        self.log_test("Database Cleanup", True, "Cleaned up test data")
                    else:
                        self.log_test("Database Retrieval", False, "No connections found")
                        
                except Exception as e:
                    self.log_test("Database Retrieval", False, f"Exception: {e}")
            else:
                self.log_test("Database Storage", False, "Failed to store connection")
                
        except Exception as e:
            self.log_test("Database Operations Test", False, f"Exception: {e}")
    
    def print_summary(self):
        """Print test summary"""
        print("\n📊 Test Summary")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['details']}")
        
        print(f"\n🎯 Flow Assessment:")
        if passed_tests >= total_tests * 0.8:  # 80% pass rate
            print("✅ The Composio MCP integration flow is working well!")
            print("   Ready for production implementation.")
        elif passed_tests >= total_tests * 0.6:  # 60% pass rate
            print("⚠️  The integration has some issues but core functionality works.")
            print("   Address failed tests before production deployment.")
        else:
            print("❌ The integration has significant issues.")
            print("   Major fixes needed before this can be used.")

async def main():
    """Run all tests"""
    print("🎯 Composio MCP Integration Flow Test")
    print("=" * 60)
    print(f"Testing against: http://localhost:8000")
    print(f"Test user ID: test_user_{int(datetime.now().timestamp())}")
    
    tester = ComposioMCPFlowTester()
    
    try:
        # Run all test suites
        await tester.test_service_direct()
        await tester.test_composio_api_direct()
        await tester.test_database_operations()
        await tester.test_api_endpoints()
        
        # Print summary
        tester.print_summary()
        
        print(f"\n💡 Next Steps:")
        print("1. If tests pass, the integration is ready for frontend implementation")
        print("2. Frontend can call /api/composio-mcp/create-connection")
        print("3. Use the returned auth_url to redirect users for authentication")
        print("4. Monitor logs for any issues during real usage")
        
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
    except Exception as e:
        print(f"\n💥 Test suite failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
