"""
Composio MCP Integration Service

This service integrates Composio's dynamic MCP URL generation with our existing
MCP architecture and OAuth flow.

Flow:
1. Frontend requests MCP connection for a specific app (Gmail, Slack, etc.)
2. Query Composio API to generate user-specific MCP URL
3. Store the URL in Supabase using existing mcp_oauth_tokens schema
4. Use existing MCP client to initiate connection and get auth URL
5. Return auth URL to frontend for one-click authentication
"""

import httpx
import uuid
import json
import base64
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from fastapi import HTTPException
from utils.logger import logger
from supabase import create_client, Client
import os

# Supabase configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")


@dataclass
class ComposioMCPConnection:
    """Result of Composio MCP connection creation"""

    success: bool
    app_key: str
    mcp_url: Optional[str] = None
    session_uuid: Optional[str] = None
    auth_url: Optional[str] = None
    error: Optional[str] = None
    qualified_name: Optional[str] = None


class ComposioMCPService:
    """Service for integrating Composio MCP URLs with existing architecture"""

    def __init__(self):
        self.composio_base_url = "https://mcp.composio.dev"
        self.timeout = 30.0
        self.supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_KEY)

    def _generate_session_uuid(self, user_id: str, app_key: str) -> str:
        """Generate a unique session UUID for user-app combination"""
        # Create deterministic but unique session ID
        session_data = f"{user_id}_{app_key}_{uuid.uuid4()}"
        return str(uuid.uuid5(uuid.NAMESPACE_DNS, session_data))

    async def _generate_composio_mcp_url(
        self, app_key: str, session_uuid: str
    ) -> Optional[str]:
        """Generate MCP URL from Composio API"""
        url = f"{self.composio_base_url}/api/apps/{app_key}/install"

        # Include framework parameter as required by Composio API
        payload = {"uuid": session_uuid, "framework": "mcp"}
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json, text/plain, */*",
            "Origin": "https://mcp.composio.dev",
            "Referer": f"https://mcp.composio.dev/{app_key}/{session_uuid}",
            "User-Agent": "Atlas-Agents/1.0",
        }

        cookies = {
            "uuid": session_uuid,
            "isActiveUser": session_uuid,
        }

        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                # Step 1: Install/register the session
                install_response = await client.post(
                    url, json=payload, headers=headers, cookies=cookies
                )

                if install_response.status_code != 200:
                    logger.error(
                        f"Install failed: {install_response.status_code} - {install_response.text}"
                    )
                    return None

                logger.info(f"Install successful: {install_response.json()}")

                # Step 2: Get the actual MCP URL from status endpoint
                status_url = f"{self.composio_base_url}/api/apps/{app_key}"
                status_response = await client.get(
                    status_url,
                    params={"uuid": session_uuid},
                    headers=headers,
                    cookies=cookies,
                )

                if status_response.status_code == 200:
                    status_data = status_response.json()
                    # The MCP URL is in the sseUrl field (despite the name, it's actually the MCP URL)
                    mcp_url = status_data.get("sseUrl")
                    if mcp_url:
                        logger.info(f"Got MCP URL from status: {mcp_url}")
                        return mcp_url
                    else:
                        logger.warning(f"No sseUrl in status response: {status_data}")
                else:
                    logger.error(
                        f"Status check failed: {status_response.status_code} - {status_response.text}"
                    )

                # Fallback: construct URL from known pattern
                fallback_url = f"{self.composio_base_url}/partner/composio/{app_key}/mcp?customerId={session_uuid}"
                logger.info(f"Using fallback MCP URL: {fallback_url}")
                return fallback_url

        except Exception as e:
            logger.error(f"Error generating Composio MCP URL: {e}")
            return None

    def _extract_mcp_url(
        self, response_data: Dict, app_key: str, session_uuid: str
    ) -> Optional[str]:
        """Extract MCP URL from Composio response"""
        # Try various patterns for MCP URLs
        possible_patterns = [
            response_data.get("sseUrl"),
            response_data.get("mcp_url"),
            response_data.get("url"),
            response_data.get("data", {}).get("sseUrl"),
            response_data.get("data", {}).get("mcp_url"),
            response_data.get("data", {}).get("url"),
            # Construct from known patterns
            f"https://mcp.composio.dev/partner/composio/{app_key}/mcp?customerId={session_uuid}",
            f"https://mcp.composio.dev/partner/composio/{app_key}/sse?customerId={session_uuid}",
        ]

        for url in possible_patterns:
            if url and isinstance(url, str) and url.startswith("https://"):
                return url

        return None

    async def _store_mcp_connection(
        self, user_id: str, app_key: str, mcp_url: str, session_uuid: str
    ) -> bool:
        """Store MCP connection in Supabase using existing schema"""
        try:
            # Use qualified_name format similar to existing MCP servers
            qualified_name = f"composio/{app_key}"

            # Normalize user ID to proper UUID format
            actual_user_id = self._normalize_user_id(user_id)
            if actual_user_id != user_id:
                logger.warning(
                    f"Converting test user ID {user_id} to UUID {actual_user_id}"
                )

            # Store in mcp_oauth_tokens table (reusing existing schema)
            # We'll use this table to track Composio connections even though
            # the actual OAuth happens through the MCP URL itself
            data = {
                "user_id": actual_user_id,
                "qualified_name": qualified_name,
                "access_token": mcp_url,  # Store MCP URL as "token"
                "refresh_token": session_uuid,  # Store session UUID
                "scope": f"composio_{app_key}",
                "expires_at": None,  # Composio URLs don't expire in traditional sense
            }

            # Upsert to handle existing connections
            result = (
                self.supabase.table("mcp_oauth_tokens")
                .upsert(data, on_conflict="user_id,qualified_name")
                .execute()
            )

            logger.info(
                f"Stored Composio MCP connection for user {user_id}, app {app_key}"
            )
            return True

        except Exception as e:
            logger.error(f"Error storing MCP connection: {e}")
            return False

    async def _initiate_mcp_connection(
        self, mcp_url: str, app_key: str
    ) -> Optional[str]:
        """Use existing MCP architecture to initiate connection and get auth URL"""
        try:
            # Import here to avoid circular imports
            from mcp.client.sse import sse_client
            from mcp.client.streamable_http import streamablehttp_client
            from mcp import ClientSession
            import asyncio

            logger.info(f"Initiating MCP connection to {mcp_url}")

            # For Composio URLs, we need to try connecting to see what happens
            # The URL might be:
            # 1. A direct MCP server that we can connect to
            # 2. An auth URL that redirects to OAuth
            # 3. A server that returns auth info in its response

            auth_url = None

            # Try SSE connection first (most Composio URLs use SSE)
            if "sse" in mcp_url or "/sse" in mcp_url:
                try:
                    logger.info(f"Attempting SSE connection to {mcp_url}")
                    async with asyncio.timeout(10):  # 10 second timeout
                        async with sse_client(mcp_url) as (read, write):
                            async with ClientSession(read, write) as session:
                                await session.initialize()
                                logger.info("SSE MCP session initialized successfully")

                                # Try to list tools to see if we get auth info
                                try:
                                    tools_result = await session.list_tools()
                                    logger.info(
                                        f"Got {len(tools_result.tools)} tools from MCP server"
                                    )

                                    # Check if any tools provide auth information
                                    for tool in tools_result.tools:
                                        if (
                                            "auth" in tool.name.lower()
                                            or "connect" in tool.name.lower()
                                        ):
                                            logger.info(
                                                f"Found auth-related tool: {tool.name}"
                                            )
                                            # This tool might provide auth URL
                                            # For now, we'll use the MCP URL as the auth URL
                                            auth_url = mcp_url
                                            break

                                    if not auth_url:
                                        # No specific auth tool found, use the MCP URL itself
                                        auth_url = mcp_url

                                except Exception as e:
                                    logger.warning(
                                        f"Could not list tools from MCP server: {e}"
                                    )
                                    # Even if we can't list tools, the connection worked
                                    auth_url = mcp_url

                except asyncio.TimeoutError:
                    logger.warning(f"SSE connection to {mcp_url} timed out")
                except Exception as e:
                    logger.warning(f"SSE connection failed: {e}")

            # Try HTTP connection if SSE failed or if URL suggests HTTP
            if not auth_url and ("mcp" in mcp_url or "http" in mcp_url):
                try:
                    logger.info(f"Attempting HTTP connection to {mcp_url}")
                    async with asyncio.timeout(10):  # 10 second timeout
                        async with streamablehttp_client(mcp_url) as (read, write, _):
                            async with ClientSession(read, write) as session:
                                await session.initialize()
                                logger.info("HTTP MCP session initialized successfully")

                                # Try to list tools
                                try:
                                    tools_result = await session.list_tools()
                                    logger.info(
                                        f"Got {len(tools_result.tools)} tools from HTTP MCP server"
                                    )
                                    auth_url = mcp_url
                                except Exception as e:
                                    logger.warning(
                                        f"Could not list tools from HTTP MCP server: {e}"
                                    )
                                    auth_url = mcp_url

                except asyncio.TimeoutError:
                    logger.warning(f"HTTP connection to {mcp_url} timed out")
                except Exception as e:
                    logger.warning(f"HTTP connection failed: {e}")

            # If we couldn't connect via MCP protocols, the URL might be a direct auth URL
            if not auth_url:
                logger.info(
                    f"Could not connect via MCP protocols, treating {mcp_url} as auth URL"
                )
                auth_url = mcp_url

            logger.info(f"Returning auth URL: {auth_url}")
            return auth_url

        except Exception as e:
            logger.error(f"Error initiating MCP connection: {e}")
            # Even if there's an error, return the URL - it might still work for auth
            return mcp_url

    async def get_or_create_user_mcp_connection(
        self, user_id: str, app_key: str
    ) -> ComposioMCPConnection:
        """
        Get existing MCP connection from Supabase or create new one if doesn't exist.
        This ensures we don't regenerate URLs unnecessarily and provides persistence.
        """
        try:
            logger.info(
                f"Getting or creating Composio MCP connection for user {user_id}, app {app_key}"
            )
            qualified_name = f"composio/{app_key}"

            # Step 1: Check if connection already exists in Supabase
            actual_user_id = self._normalize_user_id(user_id)
            try:
                existing_result = (
                    self.supabase.table("mcp_oauth_tokens")
                    .select("*")
                    .eq("user_id", actual_user_id)
                    .eq("qualified_name", qualified_name)
                    .execute()
                )

                if existing_result.data:
                    existing_connection = existing_result.data[0]
                    logger.info(
                        f"Found existing MCP connection for user {user_id}, app {app_key}"
                    )

                    return ComposioMCPConnection(
                        success=True,
                        app_key=app_key,
                        mcp_url=existing_connection[
                            "access_token"
                        ],  # MCP URL stored as access_token
                        session_uuid=existing_connection[
                            "refresh_token"
                        ],  # Session UUID stored as refresh_token
                        auth_url=existing_connection[
                            "access_token"
                        ],  # For now, MCP URL is the auth URL
                        qualified_name=qualified_name,
                    )
            except Exception as e:
                logger.warning(f"Error checking existing connection: {e}")
                # Continue to create new connection

            # Step 2: No existing connection found, create new one
            logger.info(f"No existing connection found, creating new MCP connection")

            # Generate session UUID
            session_uuid = self._generate_session_uuid(user_id, app_key)

            # Generate MCP URL from Composio
            mcp_url = await self._generate_composio_mcp_url(app_key, session_uuid)
            if not mcp_url:
                return ComposioMCPConnection(
                    success=False,
                    app_key=app_key,
                    error="Failed to generate MCP URL from Composio",
                )

            # Step 3: Store connection in Supabase
            stored = await self._store_mcp_connection(
                user_id, app_key, mcp_url, session_uuid
            )
            if not stored:
                return ComposioMCPConnection(
                    success=False,
                    app_key=app_key,
                    error="Failed to store MCP connection in database",
                )

            # Step 4: Return success with connection details
            return ComposioMCPConnection(
                success=True,
                app_key=app_key,
                mcp_url=mcp_url,
                session_uuid=session_uuid,
                auth_url=mcp_url,  # For now, MCP URL is the auth URL
                qualified_name=qualified_name,
            )

        except Exception as e:
            logger.error(f"Error in get_or_create_user_mcp_connection: {e}")
            return ComposioMCPConnection(success=False, app_key=app_key, error=str(e))

    async def create_user_mcp_connection_simple(
        self, user_id: str, app_key: str
    ) -> ComposioMCPConnection:
        """
        Simplified flow: Generate Composio MCP URL, initiate connection, return auth URL
        (No Supabase storage for testing)
        """
        try:
            logger.info(
                f"Creating Composio MCP connection for user {user_id}, app {app_key}"
            )

            # Step 1: Generate session UUID
            session_uuid = self._generate_session_uuid(user_id, app_key)

            # Step 2: Generate MCP URL from Composio
            mcp_url = await self._generate_composio_mcp_url(app_key, session_uuid)
            if not mcp_url:
                return ComposioMCPConnection(
                    success=False,
                    app_key=app_key,
                    error="Failed to generate MCP URL from Composio",
                )

            # Step 3: Initiate MCP connection to get auth URL
            auth_url = await self._initiate_mcp_connection(mcp_url, app_key)
            if not auth_url:
                return ComposioMCPConnection(
                    success=False,
                    app_key=app_key,
                    error="Failed to initiate MCP connection",
                )

            # Step 4: Return success with auth URL
            return ComposioMCPConnection(
                success=True,
                app_key=app_key,
                mcp_url=mcp_url,
                session_uuid=session_uuid,
                auth_url=auth_url,
                qualified_name=f"composio/{app_key}",
            )

        except Exception as e:
            logger.error(f"Error in create_user_mcp_connection_simple: {e}")
            return ComposioMCPConnection(success=False, app_key=app_key, error=str(e))

    async def create_user_mcp_connection(
        self, user_id: str, app_key: str
    ) -> ComposioMCPConnection:
        """
        Main method: Get existing MCP connection or create new one with persistence.

        This method now uses the get_or_create approach to avoid regenerating URLs
        and provides proper persistence through Supabase.
        """
        return await self.get_or_create_user_mcp_connection(user_id, app_key)

    def _normalize_user_id(self, user_id: str) -> str:
        """Convert user ID to proper UUID format for database operations"""
        try:
            import uuid as uuid_module

            uuid_module.UUID(user_id)
            return user_id
        except ValueError:
            import hashlib

            hash_object = hashlib.md5(user_id.encode())
            hex_dig = hash_object.hexdigest()
            return f"{hex_dig[:8]}-{hex_dig[8:12]}-{hex_dig[12:16]}-{hex_dig[16:20]}-{hex_dig[20:32]}"

    async def list_user_mcp_connections(self, user_id: str) -> List[Dict[str, Any]]:
        """
        List all Composio MCP connections for a user from Supabase.
        """
        try:
            actual_user_id = self._normalize_user_id(user_id)
            result = (
                self.supabase.table("mcp_oauth_tokens")
                .select("*")
                .eq("user_id", actual_user_id)
                .like("qualified_name", "composio/%")
                .execute()
            )

            connections = []
            for row in result.data:
                connections.append(
                    {
                        "qualified_name": row["qualified_name"],
                        "app_key": row["qualified_name"].replace("composio/", ""),
                        "mcp_url": row[
                            "access_token"
                        ],  # MCP URL stored as access_token
                        "session_uuid": row[
                            "refresh_token"
                        ],  # Session UUID stored as refresh_token
                        "created_at": row["created_at"],
                        "updated_at": row["updated_at"],
                        "scope": row["scope"],
                    }
                )

            return connections

        except Exception as e:
            logger.error(f"Error listing user MCP connections: {e}")
            return []

    async def delete_user_mcp_connection(self, user_id: str, app_key: str) -> bool:
        """
        Delete a specific Composio MCP connection for a user.
        """
        try:
            qualified_name = f"composio/{app_key}"
            actual_user_id = self._normalize_user_id(user_id)

            result = (
                self.supabase.table("mcp_oauth_tokens")
                .delete()
                .eq("user_id", actual_user_id)
                .eq("qualified_name", qualified_name)
                .execute()
            )

            if result.data:
                logger.info(
                    f"Deleted Composio MCP connection for user {user_id}, app {app_key}"
                )
                return True
            else:
                logger.warning(
                    f"No connection found to delete for user {user_id}, app {app_key}"
                )
                return False

        except Exception as e:
            logger.error(f"Error deleting MCP connection: {e}")
            return False


# Global service instance
composio_mcp_service = ComposioMCPService()
