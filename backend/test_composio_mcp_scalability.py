#!/usr/bin/env python3
"""
Scalability test for Composio MCP integration

This script tests the system's ability to handle multiple users connecting
to multiple MCP servers simultaneously, validating:
1. User isolation
2. Concurrent request handling
3. Database consistency
4. Error handling under load
5. Resource cleanup

Usage:
    python test_composio_mcp_scalability.py

Requirements:
    - Backend server running on localhost:8000
    - Valid Supabase credentials
    - Internet connection for Composio API calls
"""

import asyncio
import httpx
import json
import time
import random
from datetime import datetime
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor
import sys
import os

# Add backend to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.composio_integration import composio_mcp_service
from utils.logger import logger

class ScalabilityTester:
    """Test scalability of Composio MCP integration"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.test_apps = ["gmail", "slack", "github", "notion", "google-drive"]
        self.results = []
        self.errors = []
        
    def generate_test_users(self, count: int) -> List[str]:
        """Generate test user IDs"""
        timestamp = int(datetime.now().timestamp())
        return [f"scale_test_user_{timestamp}_{i}" for i in range(count)]
    
    async def test_single_user_multiple_apps(self, user_id: str) -> Dict[str, Any]:
        """Test one user connecting to multiple apps"""
        start_time = time.time()
        results = {
            "user_id": user_id,
            "apps_tested": [],
            "successful_connections": 0,
            "failed_connections": 0,
            "total_time": 0,
            "errors": []
        }
        
        try:
            # Test connections to all apps for this user
            tasks = []
            for app_key in self.test_apps:
                task = self.create_single_connection(user_id, app_key)
                tasks.append(task)
            
            # Run all connections concurrently
            connection_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for i, result in enumerate(connection_results):
                app_key = self.test_apps[i]
                results["apps_tested"].append(app_key)
                
                if isinstance(result, Exception):
                    results["failed_connections"] += 1
                    results["errors"].append(f"{app_key}: {str(result)}")
                elif result and result.get("success"):
                    results["successful_connections"] += 1
                else:
                    results["failed_connections"] += 1
                    results["errors"].append(f"{app_key}: Connection failed")
            
            results["total_time"] = time.time() - start_time
            
        except Exception as e:
            results["errors"].append(f"User test failed: {str(e)}")
            results["total_time"] = time.time() - start_time
        
        return results
    
    async def create_single_connection(self, user_id: str, app_key: str) -> Dict[str, Any]:
        """Create a single MCP connection"""
        try:
            connection = await composio_mcp_service.create_user_mcp_connection(user_id, app_key)
            return {
                "success": connection.success,
                "app_key": connection.app_key,
                "mcp_url": connection.mcp_url,
                "auth_url": connection.auth_url,
                "error": connection.error
            }
        except Exception as e:
            return {
                "success": False,
                "app_key": app_key,
                "error": str(e)
            }
    
    async def test_concurrent_users(self, user_count: int = 5) -> Dict[str, Any]:
        """Test multiple users creating connections concurrently"""
        print(f"\n🚀 Testing {user_count} concurrent users")
        print("=" * 50)
        
        users = self.generate_test_users(user_count)
        start_time = time.time()
        
        # Create tasks for all users
        user_tasks = []
        for user_id in users:
            task = self.test_single_user_multiple_apps(user_id)
            user_tasks.append(task)
        
        # Run all user tests concurrently
        user_results = await asyncio.gather(*user_tasks, return_exceptions=True)
        
        total_time = time.time() - start_time
        
        # Analyze results
        analysis = {
            "total_users": user_count,
            "total_time": total_time,
            "successful_users": 0,
            "failed_users": 0,
            "total_connections_attempted": 0,
            "total_connections_successful": 0,
            "total_connections_failed": 0,
            "average_time_per_user": 0,
            "errors": []
        }
        
        valid_results = []
        for i, result in enumerate(user_results):
            if isinstance(result, Exception):
                analysis["failed_users"] += 1
                analysis["errors"].append(f"User {users[i]}: {str(result)}")
            else:
                valid_results.append(result)
                if result["successful_connections"] > 0:
                    analysis["successful_users"] += 1
                else:
                    analysis["failed_users"] += 1
                
                analysis["total_connections_attempted"] += len(result["apps_tested"])
                analysis["total_connections_successful"] += result["successful_connections"]
                analysis["total_connections_failed"] += result["failed_connections"]
                analysis["errors"].extend(result["errors"])
        
        if valid_results:
            analysis["average_time_per_user"] = sum(r["total_time"] for r in valid_results) / len(valid_results)
        
        return analysis
    
    async def test_user_isolation(self) -> Dict[str, Any]:
        """Test that users are properly isolated"""
        print(f"\n🔐 Testing User Isolation")
        print("=" * 50)
        
        # Create connections for two different users with same app
        user1 = f"isolation_test_user_1_{int(time.time())}"
        user2 = f"isolation_test_user_2_{int(time.time())}"
        app_key = "gmail"
        
        try:
            # Create connections for both users
            connection1 = await composio_mcp_service.create_user_mcp_connection(user1, app_key)
            connection2 = await composio_mcp_service.create_user_mcp_connection(user2, app_key)
            
            isolation_test = {
                "success": True,
                "user1_success": connection1.success,
                "user2_success": connection2.success,
                "urls_different": False,
                "session_uuids_different": False,
                "qualified_names_same": False,
                "errors": []
            }
            
            if connection1.success and connection2.success:
                # Check that URLs are different (contain different session UUIDs)
                isolation_test["urls_different"] = connection1.mcp_url != connection2.mcp_url
                isolation_test["session_uuids_different"] = connection1.session_uuid != connection2.session_uuid
                isolation_test["qualified_names_same"] = connection1.qualified_name == connection2.qualified_name
                
                if not isolation_test["urls_different"]:
                    isolation_test["errors"].append("Users got identical MCP URLs - isolation failed!")
                
                if not isolation_test["session_uuids_different"]:
                    isolation_test["errors"].append("Users got identical session UUIDs - isolation failed!")
                    
            else:
                isolation_test["success"] = False
                if not connection1.success:
                    isolation_test["errors"].append(f"User 1 connection failed: {connection1.error}")
                if not connection2.success:
                    isolation_test["errors"].append(f"User 2 connection failed: {connection2.error}")
            
            return isolation_test
            
        except Exception as e:
            return {
                "success": False,
                "errors": [f"Isolation test failed: {str(e)}"]
            }
    
    async def test_database_consistency(self) -> Dict[str, Any]:
        """Test database consistency under concurrent load"""
        print(f"\n🗄️ Testing Database Consistency")
        print("=" * 50)
        
        try:
            from supabase import create_client
            supabase = create_client(
                os.getenv("SUPABASE_URL"),
                os.getenv("SUPABASE_SERVICE_ROLE_KEY")
            )
            
            # Count existing Composio connections
            initial_result = supabase.table("mcp_oauth_tokens").select("*").like(
                "qualified_name", "composio/%"
            ).execute()
            initial_count = len(initial_result.data)
            
            # Create multiple connections concurrently
            test_user = f"db_consistency_test_{int(time.time())}"
            apps = ["gmail", "slack", "github"]
            
            tasks = []
            for app in apps:
                task = composio_mcp_service.create_user_mcp_connection(test_user, app)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            successful_connections = sum(1 for r in results if not isinstance(r, Exception) and r.success)
            
            # Check final count
            final_result = supabase.table("mcp_oauth_tokens").select("*").like(
                "qualified_name", "composio/%"
            ).execute()
            final_count = len(final_result.data)
            
            expected_count = initial_count + successful_connections
            
            consistency_test = {
                "success": final_count == expected_count,
                "initial_count": initial_count,
                "final_count": final_count,
                "expected_count": expected_count,
                "successful_connections": successful_connections,
                "errors": []
            }
            
            if final_count != expected_count:
                consistency_test["errors"].append(
                    f"Database inconsistency: expected {expected_count}, got {final_count}"
                )
            
            # Clean up test data
            supabase.table("mcp_oauth_tokens").delete().eq("user_id", test_user).execute()
            
            return consistency_test
            
        except Exception as e:
            return {
                "success": False,
                "errors": [f"Database consistency test failed: {str(e)}"]
            }
    
    async def test_error_handling(self) -> Dict[str, Any]:
        """Test error handling with invalid inputs"""
        print(f"\n⚠️ Testing Error Handling")
        print("=" * 50)
        
        test_user = f"error_test_user_{int(time.time())}"
        error_tests = []
        
        # Test invalid app key
        try:
            result = await composio_mcp_service.create_user_mcp_connection(test_user, "invalid_app_key")
            error_tests.append({
                "test": "Invalid app key",
                "handled_gracefully": not result.success,
                "error": result.error
            })
        except Exception as e:
            error_tests.append({
                "test": "Invalid app key",
                "handled_gracefully": True,
                "error": str(e)
            })
        
        # Test empty user ID
        try:
            result = await composio_mcp_service.create_user_mcp_connection("", "gmail")
            error_tests.append({
                "test": "Empty user ID",
                "handled_gracefully": not result.success,
                "error": result.error
            })
        except Exception as e:
            error_tests.append({
                "test": "Empty user ID",
                "handled_gracefully": True,
                "error": str(e)
            })
        
        return {
            "tests": error_tests,
            "all_handled_gracefully": all(t["handled_gracefully"] for t in error_tests)
        }
    
    def print_results(self, concurrent_results: Dict, isolation_results: Dict, 
                     consistency_results: Dict, error_results: Dict):
        """Print comprehensive test results"""
        print(f"\n📊 Scalability Test Results")
        print("=" * 60)
        
        # Concurrent users results
        print(f"\n🚀 Concurrent Users Test:")
        print(f"  Users tested: {concurrent_results['total_users']}")
        print(f"  Successful users: {concurrent_results['successful_users']}")
        print(f"  Total connections attempted: {concurrent_results['total_connections_attempted']}")
        print(f"  Successful connections: {concurrent_results['total_connections_successful']}")
        print(f"  Success rate: {(concurrent_results['total_connections_successful']/concurrent_results['total_connections_attempted']*100):.1f}%")
        print(f"  Total time: {concurrent_results['total_time']:.2f}s")
        print(f"  Average time per user: {concurrent_results['average_time_per_user']:.2f}s")
        
        # Isolation results
        print(f"\n🔐 User Isolation Test:")
        if isolation_results['success']:
            print(f"  ✅ Users properly isolated")
            print(f"  ✅ Different URLs: {isolation_results['urls_different']}")
            print(f"  ✅ Different session UUIDs: {isolation_results['session_uuids_different']}")
        else:
            print(f"  ❌ Isolation test failed")
            for error in isolation_results['errors']:
                print(f"    - {error}")
        
        # Database consistency results
        print(f"\n🗄️ Database Consistency Test:")
        if consistency_results['success']:
            print(f"  ✅ Database consistent")
            print(f"  Records: {consistency_results['initial_count']} → {consistency_results['final_count']}")
        else:
            print(f"  ❌ Database inconsistency detected")
            for error in consistency_results['errors']:
                print(f"    - {error}")
        
        # Error handling results
        print(f"\n⚠️ Error Handling Test:")
        if error_results['all_handled_gracefully']:
            print(f"  ✅ All errors handled gracefully")
        else:
            print(f"  ❌ Some errors not handled properly")
        
        for test in error_results['tests']:
            status = "✅" if test['handled_gracefully'] else "❌"
            print(f"  {status} {test['test']}: {test['error']}")
        
        # Overall assessment
        print(f"\n🎯 Scalability Assessment:")
        success_rate = concurrent_results['total_connections_successful'] / concurrent_results['total_connections_attempted']
        
        if (success_rate >= 0.8 and isolation_results['success'] and 
            consistency_results['success'] and error_results['all_handled_gracefully']):
            print("✅ EXCELLENT: System handles concurrent load very well")
            print("   Ready for production with multiple users")
        elif success_rate >= 0.6 and isolation_results['success']:
            print("⚠️ GOOD: System mostly handles concurrent load")
            print("   Some optimization may be needed for high load")
        else:
            print("❌ NEEDS WORK: System has scalability issues")
            print("   Address issues before production deployment")

async def main():
    """Run scalability tests"""
    print("🎯 Composio MCP Integration Scalability Test")
    print("=" * 60)
    
    tester = ScalabilityTester()
    
    try:
        # Run all scalability tests
        concurrent_results = await tester.test_concurrent_users(user_count=3)  # Start small
        isolation_results = await tester.test_user_isolation()
        consistency_results = await tester.test_database_consistency()
        error_results = await tester.test_error_handling()
        
        # Print comprehensive results
        tester.print_results(concurrent_results, isolation_results, consistency_results, error_results)
        
        print(f"\n💡 Recommendations:")
        print("1. Monitor database connection pool under high load")
        print("2. Consider implementing rate limiting for Composio API calls")
        print("3. Add caching for frequently requested MCP URLs")
        print("4. Implement proper cleanup for failed connections")
        print("5. Add monitoring and alerting for production deployment")
        
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
    except Exception as e:
        print(f"\n💥 Scalability test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
